// Netlify function for secure database operations
const { createClient } = require('@supabase/supabase-js');

// Use service role key for server-side operations (bypasses RLS)
const supabaseUrl = process.env.SUPABASE_URL || process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.VITE_SUPABASE_ANON_KEY;

console.log('Supabase config:', {
  hasUrl: !!supabaseUrl,
  hasKey: !!supabaseKey,
  urlPrefix: supabaseUrl ? supabaseUrl.substring(0, 20) + '...' : 'missing',
  keyPrefix: supabaseKey ? supabaseKey.substring(0, 20) + '...' : 'missing'
});

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase environment variables');
  throw new Error(`Missing Supabase environment variables. URL: ${!!supabaseUrl}, Key: ${!!supabaseKey}`);
}

let supabase;
try {
  supabase = createClient(supabaseUrl, supabaseKey);
  console.log('Supabase client created successfully');
} catch (error) {
  console.error('Failed to create Supabase client:', error);
  throw error;
}

// Helper function to validate HWID
const isValidHwid = (hwid) => {
  return hwid && typeof hwid === 'string' && hwid.length > 0;
};

// Helper function to handle CORS
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'Content-Type',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS'
};

exports.handler = async (event, context) => {
  console.log('Function called:', event.httpMethod, event.path);

  // Handle CORS preflight
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers: corsHeaders,
      body: ''
    };
  }

  try {
    console.log('Request body:', event.body);

    const { action, params } = JSON.parse(event.body || '{}');

    switch (action) {
      case 'getScripts':
        return await getScripts();

      case 'rateScript':
        return await rateScript(params);

      case 'getUserRatings':
        return await getUserRatings(params);

      case 'submitScriptRequest':
        return await submitScriptRequest(params);

      case 'trackUser':
        return await trackUser(params);

      case 'checkUserStatus':
        return await checkUserStatus(params);

      case 'checkAdminStatus':
        return await checkAdminStatus(params);

      case 'adminLogin':
        return await adminLogin(params);

      case 'getAdminKeys':
        return await getAdminKeys(params);

      case 'getScriptRequests':
        return await getScriptRequests(params);

      case 'updateRequestStatus':
        return await updateRequestStatus(params);

      case 'test':
        return {
          statusCode: 200,
          headers: corsHeaders,
          body: JSON.stringify({
            message: 'API is working!',
            timestamp: new Date().toISOString(),
            env: {
              hasUrl: !!supabaseUrl,
              hasKey: !!supabaseKey,
              nodeEnv: process.env.NODE_ENV
            }
          })
        };

      default:
        return {
          statusCode: 400,
          headers: corsHeaders,
          body: JSON.stringify({ error: `Invalid action: ${action}` })
        };
    }
  } catch (error) {
    console.error('API Error:', error);
    console.error('Error stack:', error.stack);
    return {
      statusCode: 500,
      headers: corsHeaders,
      body: JSON.stringify({
        error: 'Internal server error',
        message: error.message,
        details: process.env.NODE_ENV === 'development' ? error.stack : undefined
      })
    };
  }
};

// Get all scripts with ratings
async function getScripts() {
  try {
    const { data, error } = await supabase
      .from('scripts')
      .select(`*, script_ratings ( rating )`);

    if (error) throw error;

    return {
      statusCode: 200,
      headers: corsHeaders,
      body: JSON.stringify({ data })
    };
  } catch (error) {
    return {
      statusCode: 500,
      headers: corsHeaders,
      body: JSON.stringify({ error: error.message })
    };
  }
}

// Rate a script
async function rateScript({ scriptId, rating, hwid }) {
  try {
    if (!isValidHwid(hwid) || !scriptId || rating === undefined) {
      return {
        statusCode: 400,
        headers: corsHeaders,
        body: JSON.stringify({ error: 'Missing required parameters' })
      };
    }

    const { data, error } = await supabase
      .from('script_ratings')
      .upsert({
        script_id: scriptId,
        hwid: hwid,
        rating: rating
      }, {
        onConflict: 'script_id,hwid'
      });

    if (error) throw error;

    return {
      statusCode: 200,
      headers: corsHeaders,
      body: JSON.stringify({ data })
    };
  } catch (error) {
    return {
      statusCode: 500,
      headers: corsHeaders,
      body: JSON.stringify({ error: error.message })
    };
  }
}

// Get user ratings
async function getUserRatings({ hwid }) {
  try {
    if (!isValidHwid(hwid)) {
      return {
        statusCode: 400,
        headers: corsHeaders,
        body: JSON.stringify({ error: 'Invalid HWID' })
      };
    }

    const { data, error } = await supabase
      .from('script_ratings')
      .select('script_id, rating')
      .eq('hwid', hwid);

    if (error) throw error;

    return {
      statusCode: 200,
      headers: corsHeaders,
      body: JSON.stringify({ data })
    };
  } catch (error) {
    return {
      statusCode: 500,
      headers: corsHeaders,
      body: JSON.stringify({ error: error.message })
    };
  }
}

// Submit script request
async function submitScriptRequest({ gameName, gameLink, scriptDescription, discordUsername }) {
  try {
    if (!gameName || !scriptDescription) {
      return {
        statusCode: 400,
        headers: corsHeaders,
        body: JSON.stringify({ error: 'Missing required fields' })
      };
    }

    const { data, error } = await supabase
      .from('script_requests')
      .insert({
        game_name: gameName,
        game_link: gameLink,
        script_description: scriptDescription,
        discord_username: discordUsername || null,
        status: 'Pending',
      })
      .select()
      .single();

    if (error) throw error;

    // Send Discord notification (if webhook URL is configured)
    const webhookUrl = process.env.DISCORD_WEBHOOK_URL;
    if (webhookUrl) {
      try {
        const embed = {
          title: 'New Script Request',
          color: 8535234,
          fields: [
            { name: 'Game Name', value: gameName, inline: true },
            { name: 'Discord Username', value: discordUsername || 'Not Provided', inline: true },
            { name: 'Status', value: '🕒 Pending', inline: true },
            { name: 'Game Link', value: `[Click Here](${gameLink})` },
            { name: 'Script Description', value: scriptDescription },
            { name: 'Request ID', value: `\`${data.id}\``, inline: false}
          ],
          footer: { text: '6FootScripts Request System' },
          timestamp: new Date().toISOString(),
        };

        const payload = { embeds: [embed] };

        await fetch(webhookUrl, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(payload),
        });

        console.log('Discord notification sent successfully');
      } catch (discordError) {
        console.error('Failed to send Discord notification:', discordError);
        // Don't fail the request if Discord notification fails
      }
    }

    return {
      statusCode: 200,
      headers: corsHeaders,
      body: JSON.stringify({ data })
    };
  } catch (error) {
    return {
      statusCode: 500,
      headers: corsHeaders,
      body: JSON.stringify({ error: error.message })
    };
  }
}

// Track user
async function trackUser({ hwid, path }) {
  try {
    if (!isValidHwid(hwid)) {
      return {
        statusCode: 400,
        headers: corsHeaders,
        body: JSON.stringify({ error: 'Invalid HWID' })
      };
    }

    // First ensure user exists in users table
    const { error: upsertError } = await supabase
      .from('users')
      .upsert({
        hwid: hwid,
        status: 'neutral'
      }, {
        onConflict: 'hwid',
        ignoreDuplicates: true
      });

    if (upsertError) {
      console.error("Error ensuring user exists:", upsertError);
    }

    // Track page visit
    const { data, error } = await supabase
      .from('page_visits')
      .insert({
        hwid,
        path
      });

    if (error) throw error;

    return {
      statusCode: 200,
      headers: corsHeaders,
      body: JSON.stringify({ data })
    };
  } catch (error) {
    return {
      statusCode: 500,
      headers: corsHeaders,
      body: JSON.stringify({ error: error.message })
    };
  }
}

// Check user status
async function checkUserStatus({ hwid }) {
  try {
    console.log('checkUserStatus called with hwid:', hwid);

    if (!isValidHwid(hwid)) {
      console.log('Invalid HWID provided');
      return {
        statusCode: 400,
        headers: corsHeaders,
        body: JSON.stringify({ error: 'Invalid HWID' })
      };
    }

    console.log('Checking users table for status...');

    // Check user status in users table
    const { data, error } = await supabase
      .from('users')
      .select('status')
      .eq('hwid', hwid)
      .single();

    console.log('Supabase query result:', { data, error: error?.message, code: error?.code });

    if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
      console.error('Supabase error (not PGRST116):', error);
      throw error;
    }

    // User is blacklisted if status is 'blacklisted'
    const isBlacklisted = data?.status === 'blacklisted';
    console.log('User blacklist status:', isBlacklisted);

    return {
      statusCode: 200,
      headers: corsHeaders,
      body: JSON.stringify({ isBlacklisted })
    };
  } catch (error) {
    console.error('checkUserStatus error:', error);
    return {
      statusCode: 500,
      headers: corsHeaders,
      body: JSON.stringify({ error: error.message })
    };
  }
}

// Check admin status
async function checkAdminStatus({ hwid }) {
  try {
    console.log('checkAdminStatus called with hwid:', hwid);

    if (!isValidHwid(hwid)) {
      console.log('Invalid HWID provided');
      return {
        statusCode: 400,
        headers: corsHeaders,
        body: JSON.stringify({ error: 'Invalid HWID' })
      };
    }

    console.log('Checking admin_keys table...');

    // Check if HWID is an admin
    const { data, error } = await supabase
      .from('admin_keys')
      .select('id, name')
      .eq('hwid', hwid)
      .single();

    console.log('Admin check result:', { data, error: error?.message, code: error?.code });

    if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
      console.error('Supabase error (not PGRST116):', error);
      throw error;
    }

    const isAdmin = !!data;
    console.log('User admin status:', isAdmin);

    return {
      statusCode: 200,
      headers: corsHeaders,
      body: JSON.stringify({ isAdmin })
    };
  } catch (error) {
    console.error('checkAdminStatus error:', error);
    return {
      statusCode: 500,
      headers: corsHeaders,
      body: JSON.stringify({ error: error.message })
    };
  }
}

// Admin login
async function adminLogin({ accessKey, hwid }) {
  try {
    console.log('adminLogin called with accessKey:', accessKey?.substring(0, 3) + '...', 'hwid:', hwid);

    if (!accessKey || !isValidHwid(hwid)) {
      return {
        statusCode: 400,
        headers: corsHeaders,
        body: JSON.stringify({ error: 'Missing access key or invalid HWID' })
      };
    }

    // Check if access key exists and is valid
    const { data: keyData, error: keyError } = await supabase
      .from('admin_keys')
      .select('id, name, hwid')
      .eq('access_key', accessKey)
      .single();

    if (keyError || !keyData) {
      console.log('Invalid access key:', keyError?.message);
      return {
        statusCode: 401,
        headers: corsHeaders,
        body: JSON.stringify({ error: 'Invalid access key' })
      };
    }

    // If key is not assigned to any HWID, assign it to this HWID
    if (!keyData.hwid) {
      const { error: updateError } = await supabase
        .from('admin_keys')
        .update({
          hwid: hwid,
          assigned_at: new Date().toISOString()
        })
        .eq('id', keyData.id);

      if (updateError) {
        console.error('Failed to assign key to HWID:', updateError);
        return {
          statusCode: 500,
          headers: corsHeaders,
          body: JSON.stringify({ error: 'Failed to assign access key' })
        };
      }

      console.log('Access key assigned to HWID successfully');
      return {
        statusCode: 200,
        headers: corsHeaders,
        body: JSON.stringify({
          success: true,
          name: keyData.name,
          message: 'Access key assigned successfully'
        })
      };
    }

    // If key is already assigned, check if it matches this HWID
    if (keyData.hwid === hwid) {
      console.log('HWID matches assigned key');
      return {
        statusCode: 200,
        headers: corsHeaders,
        body: JSON.stringify({
          success: true,
          name: keyData.name,
          message: 'Login successful'
        })
      };
    } else {
      console.log('HWID does not match assigned key');
      return {
        statusCode: 401,
        headers: corsHeaders,
        body: JSON.stringify({ error: 'Access key is assigned to a different device' })
      };
    }

  } catch (error) {
    console.error('adminLogin error:', error);
    return {
      statusCode: 500,
      headers: corsHeaders,
      body: JSON.stringify({ error: error.message })
    };
  }
}

// Get admin keys
async function getAdminKeys({ hwid }) {
  try {
    console.log('getAdminKeys called with hwid:', hwid);

    if (!isValidHwid(hwid)) {
      return {
        statusCode: 400,
        headers: corsHeaders,
        body: JSON.stringify({ error: 'Invalid HWID' })
      };
    }

    // First verify the user is an admin
    const { data: adminCheck, error: adminError } = await supabase
      .from('admin_keys')
      .select('id')
      .eq('hwid', hwid)
      .single();

    if (adminError || !adminCheck) {
      return {
        statusCode: 403,
        headers: corsHeaders,
        body: JSON.stringify({ error: 'Unauthorized' })
      };
    }

    // Get all admin keys
    const { data, error } = await supabase
      .from('admin_keys')
      .select('*')
      .order('created_at', { ascending: false });

    if (error) throw error;

    return {
      statusCode: 200,
      headers: corsHeaders,
      body: JSON.stringify({ data })
    };
  } catch (error) {
    console.error('getAdminKeys error:', error);
    return {
      statusCode: 500,
      headers: corsHeaders,
      body: JSON.stringify({ error: error.message })
    };
  }
}

// Get script requests
async function getScriptRequests({ hwid }) {
  try {
    console.log('getScriptRequests called with hwid:', hwid);

    if (!isValidHwid(hwid)) {
      return {
        statusCode: 400,
        headers: corsHeaders,
        body: JSON.stringify({ error: 'Invalid HWID' })
      };
    }

    // First verify the user is an admin
    const { data: adminCheck, error: adminError } = await supabase
      .from('admin_keys')
      .select('id')
      .eq('hwid', hwid)
      .single();

    if (adminError || !adminCheck) {
      return {
        statusCode: 403,
        headers: corsHeaders,
        body: JSON.stringify({ error: 'Unauthorized' })
      };
    }

    // Get all script requests
    const { data, error } = await supabase
      .from('script_requests')
      .select('*')
      .order('created_at', { ascending: false });

    if (error) throw error;

    return {
      statusCode: 200,
      headers: corsHeaders,
      body: JSON.stringify({ data })
    };
  } catch (error) {
    console.error('getScriptRequests error:', error);
    return {
      statusCode: 500,
      headers: corsHeaders,
      body: JSON.stringify({ error: error.message })
    };
  }
}

// Update script request status
async function updateRequestStatus({ hwid, requestId, status }) {
  try {
    console.log('updateRequestStatus called:', { hwid, requestId, status });

    if (!isValidHwid(hwid) || !requestId || !status) {
      return {
        statusCode: 400,
        headers: corsHeaders,
        body: JSON.stringify({ error: 'Missing required parameters' })
      };
    }

    // First verify the user is an admin
    const { data: adminCheck, error: adminError } = await supabase
      .from('admin_keys')
      .select('id')
      .eq('hwid', hwid)
      .single();

    if (adminError || !adminCheck) {
      return {
        statusCode: 403,
        headers: corsHeaders,
        body: JSON.stringify({ error: 'Unauthorized' })
      };
    }

    // Update the request status
    const { data, error } = await supabase
      .from('script_requests')
      .update({ status })
      .eq('id', requestId)
      .select()
      .single();

    if (error) throw error;

    return {
      statusCode: 200,
      headers: corsHeaders,
      body: JSON.stringify({ data })
    };
  } catch (error) {
    console.error('updateRequestStatus error:', error);
    return {
      statusCode: 500,
      headers: corsHeaders,
      body: JSON.stringify({ error: error.message })
    };
  }
}
