// Netlify function for secure database operations
const { createClient } = require('@supabase/supabase-js');

// Try to get environment variables from different sources
const supabaseUrl = process.env.SUPABASE_URL || process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase environment variables:', {
    hasUrl: !!supabaseUrl,
    hasKey: !!supabaseKey,
    envVars: Object.keys(process.env).filter(key => key.includes('SUPABASE'))
  });
}

const supabase = createClient(supabaseUrl, supabaseKey);

// Helper function to validate HWID
const isValidHwid = (hwid) => {
  return hwid && typeof hwid === 'string' && hwid.length > 0;
};

// Helper function to handle CORS
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'Content-Type',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS'
};

exports.handler = async (event, context) => {
  // Handle CORS preflight
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers: corsHeaders,
      body: ''
    };
  }

  try {
    const { action, params } = JSON.parse(event.body || '{}');

    switch (action) {
      case 'getScripts':
        return await getScripts();

      case 'rateScript':
        return await rateScript(params);

      case 'getUserRatings':
        return await getUserRatings(params);

      case 'submitScriptRequest':
        return await submitScriptRequest(params);

      case 'trackUser':
        return await trackUser(params);

      case 'checkUserStatus':
        return await checkUserStatus(params);

      default:
        return {
          statusCode: 400,
          headers: corsHeaders,
          body: JSON.stringify({ error: 'Invalid action' })
        };
    }
  } catch (error) {
    console.error('API Error:', error);
    return {
      statusCode: 500,
      headers: corsHeaders,
      body: JSON.stringify({ error: 'Internal server error' })
    };
  }
};

// Get all scripts with ratings
async function getScripts() {
  try {
    const { data, error } = await supabase
      .from('scripts')
      .select(`*, script_ratings ( rating )`);

    if (error) throw error;

    return {
      statusCode: 200,
      headers: corsHeaders,
      body: JSON.stringify({ data })
    };
  } catch (error) {
    return {
      statusCode: 500,
      headers: corsHeaders,
      body: JSON.stringify({ error: error.message })
    };
  }
}

// Rate a script
async function rateScript({ scriptId, rating, hwid }) {
  try {
    if (!isValidHwid(hwid) || !scriptId || rating === undefined) {
      return {
        statusCode: 400,
        headers: corsHeaders,
        body: JSON.stringify({ error: 'Missing required parameters' })
      };
    }

    const { data, error } = await supabase
      .from('script_ratings')
      .upsert({
        script_id: scriptId,
        hwid: hwid,
        rating: rating
      }, {
        onConflict: 'script_id,hwid'
      });

    if (error) throw error;

    return {
      statusCode: 200,
      headers: corsHeaders,
      body: JSON.stringify({ data })
    };
  } catch (error) {
    return {
      statusCode: 500,
      headers: corsHeaders,
      body: JSON.stringify({ error: error.message })
    };
  }
}

// Get user ratings
async function getUserRatings({ hwid }) {
  try {
    if (!isValidHwid(hwid)) {
      return {
        statusCode: 400,
        headers: corsHeaders,
        body: JSON.stringify({ error: 'Invalid HWID' })
      };
    }

    const { data, error } = await supabase
      .from('script_ratings')
      .select('script_id, rating')
      .eq('hwid', hwid);

    if (error) throw error;

    return {
      statusCode: 200,
      headers: corsHeaders,
      body: JSON.stringify({ data })
    };
  } catch (error) {
    return {
      statusCode: 500,
      headers: corsHeaders,
      body: JSON.stringify({ error: error.message })
    };
  }
}

// Submit script request
async function submitScriptRequest({ gameName, gameLink, scriptDescription, discordUsername }) {
  try {
    if (!gameName || !scriptDescription) {
      return {
        statusCode: 400,
        headers: corsHeaders,
        body: JSON.stringify({ error: 'Missing required fields' })
      };
    }

    const { data, error } = await supabase
      .from('script_requests')
      .insert({
        game_name: gameName,
        game_link: gameLink,
        script_description: scriptDescription,
        discord_username: discordUsername || null,
        status: 'Pending',
      })
      .select()
      .single();

    if (error) throw error;

    return {
      statusCode: 200,
      headers: corsHeaders,
      body: JSON.stringify({ data })
    };
  } catch (error) {
    return {
      statusCode: 500,
      headers: corsHeaders,
      body: JSON.stringify({ error: error.message })
    };
  }
}

// Track user
async function trackUser({ hwid, path }) {
  try {
    if (!isValidHwid(hwid)) {
      return {
        statusCode: 400,
        headers: corsHeaders,
        body: JSON.stringify({ error: 'Invalid HWID' })
      };
    }

    // Implementation depends on your user tracking logic
    // This is a placeholder - adjust based on your needs
    const { data, error } = await supabase
      .from('user_tracking')
      .upsert({
        hwid: hwid,
        last_path: path,
        last_seen: new Date().toISOString()
      }, {
        onConflict: 'hwid'
      });

    if (error) throw error;

    return {
      statusCode: 200,
      headers: corsHeaders,
      body: JSON.stringify({ data })
    };
  } catch (error) {
    return {
      statusCode: 500,
      headers: corsHeaders,
      body: JSON.stringify({ error: error.message })
    };
  }
}

// Check user status
async function checkUserStatus({ hwid }) {
  try {
    if (!isValidHwid(hwid)) {
      return {
        statusCode: 400,
        headers: corsHeaders,
        body: JSON.stringify({ error: 'Invalid HWID' })
      };
    }

    // Check if user is blacklisted
    const { data, error } = await supabase
      .from('blacklisted_users')
      .select('*')
      .eq('hwid', hwid)
      .single();

    if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
      throw error;
    }

    const isBlacklisted = !!data;

    return {
      statusCode: 200,
      headers: corsHeaders,
      body: JSON.stringify({ isBlacklisted })
    };
  } catch (error) {
    return {
      statusCode: 500,
      headers: corsHeaders,
      body: JSON.stringify({ error: error.message })
    };
  }
}

exports.handler = async (event, context) => {
  // Set CORS headers
  const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type',
    'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
  };

  // Handle preflight requests
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers,
      body: '',
    };
  }

  try {
    const { data, error } = await supabase.from('users').select('*');
    
    if (error) {
      return {
        statusCode: 400,
        headers,
        body: JSON.stringify({ error: error.message }),
      };
    }

    return {
      statusCode: 200,
      headers,
      body: JSON.stringify({ data }),
    };
  } catch (err) {
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ error: 'Internal server error' }),
    };
  }
};
