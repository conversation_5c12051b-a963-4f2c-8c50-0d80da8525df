// Secure API client that uses Netlify functions instead of direct Supabase calls
const API_BASE_URL = '/.netlify/functions';

class ApiClient {
  async request(action, params = {}) {
    const url = `${API_BASE_URL}/getUserData`;
    const config = {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ action, params }),
    };

    const response = await fetch(url, config);

    if (!response.ok) {
      throw new Error(`API Error: ${response.status} ${response.statusText}`);
    }

    const result = await response.json();

    if (result.error) {
      throw new Error(result.error);
    }

    return result;
  }

  // User tracking methods
  async trackUser(hwid, path) {
    return this.request('trackUser', { hwid, path });
  }

  async checkUserStatus(hwid) {
    return this.request('checkUserStatus', { hwid });
  }

  async checkAdminStatus(hwid) {
    return this.request('checkAdminStatus', { hwid });
  }

  async adminLogin(accessKey, hwid) {
    return this.request('adminLogin', { accessKey, hwid });
  }

  async test() {
    return this.request('test');
  }

  // Admin methods
  async getAdminKeys(hwid) {
    return this.request('getAdminKeys', { hwid });
  }

  async getScriptRequests(hwid) {
    return this.request('getScriptRequests', { hwid });
  }

  async updateRequestStatus(hwid, requestId, status) {
    return this.request('updateRequestStatus', { hwid, requestId, status });
  }

  // Script methods
  async getScripts() {
    return this.request('getScripts');
  }

  async rateScript(scriptId, rating, hwid) {
    return this.request('rateScript', { scriptId, rating, hwid });
  }

  async getUserRatings(hwid) {
    return this.request('getUserRatings', { hwid });
  }

  // Script request methods
  async submitScriptRequest(gameName, gameLink, scriptDescription, discordUsername) {
    return this.request('submitScriptRequest', {
      gameName,
      gameLink,
      scriptDescription,
      discordUsername
    });
  }
}

export const apiClient = new ApiClient();
