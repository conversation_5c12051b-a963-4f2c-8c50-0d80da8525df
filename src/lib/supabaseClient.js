import { createClient } from '@supabase/supabase-js';

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;


let supabase = null;

if (supabaseUrl && supabaseAnonKey) {
  supabase = createClient(supabaseUrl, supabaseAnonKey);
} else {
  console.warn('Supabase environment variables not found. Admin functions may not work.');
  supabase = {
    functions: {
      invoke: () => Promise.reject(new Error('Supabase not configured'))
    },
    rpc: () => Promise.reject(new Error('Supabase not configured'))
  };
}

export { supabase };