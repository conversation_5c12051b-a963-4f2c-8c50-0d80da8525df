import { createClient } from '@supabase/supabase-js';

// Note: This client is now only used for admin functions and edge functions
// Regular database operations should use the secure API client instead
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

// For development, we'll still allow the client to be created without env vars
// In production, these should be set in Netlify environment variables
let supabase = null;

if (supabaseUrl && supabaseAnonKey) {
  supabase = createClient(supabaseUrl, supabaseAnonKey);
} else {
  console.warn('Supabase environment variables not found. Admin functions may not work.');
  // Create a mock client for development
  supabase = {
    functions: {
      invoke: () => Promise.reject(new Error('Supabase not configured'))
    },
    rpc: () => Promise.reject(new Error('Supabase not configured'))
  };
}

export { supabase };