
import React, { useState, useEffect } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { apiClient } from '@/lib/apiClient';
import { getHwid } from '@/lib/hwid';
import GlobalLoader from '@/components/GlobalLoader';

const AdminAccessGuard = ({ children }) => {
  const [isAuthorized, setIsAuthorized] = useState(null);
  const location = useLocation();

  useEffect(() => {
    const checkAuthorization = async () => {
      const hwid = getHwid();
      if (!hwid) {
        setIsAuthorized(false);
        return;
      }

      // If user is already authenticated via session, trust it.
      const isAuthenticated = sessionStorage.getItem('6footscripts_admin_auth') === 'true';
      if (isAuthenticated) {
        setIsAuthorized(true);
        return;
      }

      // Otherwise, check if their HWID is registered as an admin using secure API
      try {
        const result = await apiClient.checkAdminStatus(hwid);
        setIsAuthorized(result.isAdmin);
      } catch (error) {
        console.error('Admin check failed:', error);
        setIsAuthorized(false);
      }
    };

    checkAuthorization();
  }, []);

  if (isAuthorized === null) {
    return <GlobalLoader />;
  }

  if (!isAuthorized) {
    return <Navigate to="/" replace />;
  }

  // If authorized but not logged in, and not on the login page, redirect to login.
  const isAuthenticated = sessionStorage.getItem('6footscripts_admin_auth') === 'true';
  if (!isAuthenticated && location.pathname !== '/admin/login') {
    return <Navigate to="/admin/login" replace />;
  }

  return children;
};

export default AdminAccessGuard;
